"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = require("../lib/auth");
const environment_1 = require("../config/environment");
const router = (0, express_1.Router)();
router.all("/*", async (req, res) => {
    try {
        const url = new URL(req.url, `http://${req.get("host")}`);
        const headers = new Headers();
        Object.entries(req.headers).forEach(([key, value]) => {
            if (typeof value === "string") {
                headers.set(key, value);
            }
            else if (Array.isArray(value)) {
                headers.set(key, value.join(", "));
            }
        });
        const requestInit = {
            method: req.method,
            headers,
        };
        if (req.method !== "GET" && req.method !== "HEAD" && req.body) {
            requestInit.body = JSON.stringify(req.body);
        }
        const webRequest = new Request(url.toString(), requestInit);
        const response = await auth_1.auth.handler(webRequest);
        res.status(response.status || 200);
        if (response.headers) {
            response.headers.forEach((value, key) => {
                res.setHeader(key, value);
            });
        }
        if (response.body) {
            if (response.status === 302 || response.status === 301) {
                const location = response.headers.get("location");
                if (location) {
                    return res.redirect(location);
                }
            }
            const bodyText = await response.text();
            if (bodyText) {
                try {
                    const jsonBody = JSON.parse(bodyText);
                    return res.json(jsonBody);
                }
                catch {
                    return res.send(bodyText);
                }
            }
        }
        res.end();
    }
    catch (error) {
        console.error("Better Auth error:", error);
        res.status(500).json({
            success: false,
            message: "Authentication error",
            error: environment_1.config.app.isDevelopment ? error : undefined,
        });
    }
});
exports.default = router;
//# sourceMappingURL=better-auth.routes.js.map