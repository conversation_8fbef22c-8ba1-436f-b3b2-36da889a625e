"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = exports.env = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const zod_1 = require("zod");
dotenv_1.default.config();
const envSchema = zod_1.z.object({
    NODE_ENV: zod_1.z
        .enum(["development", "production", "test"])
        .default("development"),
    PORT: zod_1.z.string().transform(Number).default("3001"),
    MONGODB_URI: zod_1.z.string().default("mongodb://localhost:27017/bookmarked"),
    MONGODB_TEST_URI: zod_1.z
        .string()
        .default("mongodb://localhost:27017/bookmarked-test"),
    JWT_SECRET: zod_1.z.string().min(32, "JWT_SECRET must be at least 32 characters"),
    JWT_EXPIRES_IN: zod_1.z.string().default("24h"),
    BCRYPT_ROUNDS: zod_1.z.string().transform(Number).default("12"),
    FRONTEND_URL: zod_1.z.string().url().default("http://localhost:5173"),
    ALLOWED_ORIGINS: zod_1.z
        .string()
        .default("http://localhost:5173,http://localhost:3000"),
    RATE_LIMIT_WINDOW_MS: zod_1.z.string().transform(Number).default("900000"),
    RATE_LIMIT_MAX_REQUESTS: zod_1.z.string().transform(Number).default("100"),
    SMTP_HOST: zod_1.z.string().optional(),
    SMTP_PORT: zod_1.z.string().transform(Number).optional(),
    SMTP_USER: zod_1.z.string().optional(),
    SMTP_PASS: zod_1.z.string().optional(),
    GOOGLE_CLIENT_ID: zod_1.z.string().optional(),
    GOOGLE_CLIENT_SECRET: zod_1.z.string().optional(),
});
const envResult = envSchema.safeParse(process.env);
if (!envResult.success) {
    console.error("❌ Invalid environment variables:");
    console.error(envResult.error.format());
    process.exit(1);
}
exports.env = envResult.data;
exports.config = {
    app: {
        name: "Bookmarked API",
        version: "1.0.0",
        env: exports.env.NODE_ENV,
        port: exports.env.PORT,
        isDevelopment: exports.env.NODE_ENV === "development",
        isProduction: exports.env.NODE_ENV === "production",
        isTest: exports.env.NODE_ENV === "test",
    },
    database: {
        uri: exports.env.NODE_ENV === "test" ? exports.env.MONGODB_TEST_URI : exports.env.MONGODB_URI,
        options: {
            maxPoolSize: 10,
            serverSelectionTimeoutMS: 5000,
            socketTimeoutMS: 45000,
        },
    },
    auth: {
        jwtSecret: exports.env.JWT_SECRET,
        jwtExpiresIn: exports.env.JWT_EXPIRES_IN,
        bcryptRounds: exports.env.BCRYPT_ROUNDS,
    },
    cors: {
        origin: exports.env.ALLOWED_ORIGINS.split(",").map((origin) => origin.trim()),
        credentials: true,
    },
    rateLimit: {
        windowMs: exports.env.RATE_LIMIT_WINDOW_MS,
        max: exports.env.RATE_LIMIT_MAX_REQUESTS,
        message: "Too many requests from this IP, please try again later.",
    },
    email: {
        host: exports.env.SMTP_HOST,
        port: exports.env.SMTP_PORT,
        user: exports.env.SMTP_USER,
        pass: exports.env.SMTP_PASS,
        enabled: !!(exports.env.SMTP_HOST && exports.env.SMTP_USER && exports.env.SMTP_PASS),
    },
    oauth: {
        google: {
            clientId: exports.env.GOOGLE_CLIENT_ID,
            clientSecret: exports.env.GOOGLE_CLIENT_SECRET,
            enabled: !!(exports.env.GOOGLE_CLIENT_ID && exports.env.GOOGLE_CLIENT_SECRET),
        },
    },
};
//# sourceMappingURL=environment.js.map