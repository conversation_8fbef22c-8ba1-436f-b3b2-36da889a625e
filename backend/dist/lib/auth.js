"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.auth = void 0;
const better_auth_1 = require("better-auth");
const mongodb_1 = require("mongodb");
const mongodb_2 = require("better-auth/adapters/mongodb");
const environment_1 = require("../config/environment");
const client = new mongodb_1.MongoClient(environment_1.env.MONGODB_URI);
const db = client.db();
exports.auth = (0, better_auth_1.betterAuth)({
    database: (0, mongodb_2.mongodbAdapter)(db),
    secret: environment_1.env.JWT_SECRET,
    baseURL: environment_1.env.NODE_ENV === "production"
        ? "https://api.bookmarked.app"
        : "http://localhost:3001",
    emailAndPassword: {
        enabled: true,
        requireEmailVerification: false,
    },
    socialProviders: environment_1.config.oauth.google.enabled
        ? {
            google: {
                clientId: environment_1.config.oauth.google.clientId,
                clientSecret: environment_1.config.oauth.google.clientSecret,
            },
        }
        : {},
    session: {
        expiresIn: 60 * 60 * 24,
        updateAge: 60 * 60 * 24,
        cookieCache: {
            enabled: true,
            maxAge: 60 * 60 * 24,
        },
    },
    advanced: {
        generateId: () => {
            return (new Date().getTime().toString(36) + Math.random().toString(36).substr(2));
        },
    },
});
//# sourceMappingURL=auth.js.map