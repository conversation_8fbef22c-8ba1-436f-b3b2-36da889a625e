import { betterAuth } from "better-auth";
import { MongoClient } from "mongodb";
import { mongodbAdapter } from "better-auth/adapters/mongodb";
import { env, config } from "../config/environment";

// Create MongoDB client
const client = new MongoClient(env.MONGODB_URI);
const db = client.db();

export const auth = betterAuth({
  database: mongodbAdapter(db),

  // Base configuration
  secret: env.JWT_SECRET,
  baseURL:
    env.NODE_ENV === "production"
      ? "https://api.bookmarked.app"
      : "http://localhost:3001",

  // Enable email/password authentication
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false, // Set to true if you want email verification
  },

  // Configure social providers
  socialProviders: config.oauth.google.enabled
    ? {
        google: {
          clientId: config.oauth.google.clientId!,
          clientSecret: config.oauth.google.clientSecret!,
        },
      }
    : {},

  // Session configuration
  session: {
    expiresIn: 60 * 60 * 24, // 24 hours
    updateAge: 60 * 60 * 24, // 24 hours
    cookieCache: {
      enabled: true,
      maxAge: 60 * 60 * 24, // 24 hours
    },
  },

  // Advanced options
  advanced: {
    generateId: () => {
      // Use MongoDB ObjectId-like format for consistency
      return (
        new Date().getTime().toString(36) + Math.random().toString(36).substr(2)
      );
    },
  },
});

export type Session = typeof auth.$Infer.Session;
