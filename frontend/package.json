{"name": "bookmarked-frontend", "version": "1.0.0", "description": "Frontend React application for Bookmarked", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "axios": "^1.6.2", "better-auth": "^1.2.12", "bookmarked-types": "file:../packages/bookmarked-types", "lucide-react": "^0.525.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-router-dom": "^6.20.1", "zod": "^3.22.4"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "vite": "^5.0.8"}, "keywords": ["react", "frontend", "typescript", "vite", "bookmarked"], "author": "<PERSON><PERSON>", "license": "MIT"}