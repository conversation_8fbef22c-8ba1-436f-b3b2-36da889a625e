import { Router, Request, Response } from "express";
import { auth } from "../lib/auth";
import { config } from "../config/environment";

const router = Router();

/**
 * Better Auth handler for all auth routes
 * Handles: /api/auth/sign-in, /api/auth/sign-up, /api/auth/sign-out,
 *          /api/auth/google, /api/auth/callback/google, etc.
 */
router.all("/*", async (req: Request, res: Response) => {
  try {
    // Convert Express request to Web API Request
    const url = new URL(req.url, `http://${req.get("host")}`);

    // Convert headers to Headers object
    const headers = new Headers();
    Object.entries(req.headers).forEach(([key, value]) => {
      if (typeof value === "string") {
        headers.set(key, value);
      } else if (Array.isArray(value)) {
        headers.set(key, value.join(", "));
      }
    });

    const requestInit: RequestInit = {
      method: req.method,
      headers,
    };

    // Add body for non-GET/HEAD requests
    if (req.method !== "GET" && req.method !== "HEAD" && req.body) {
      requestInit.body = JSON.stringify(req.body);
    }

    const webRequest = new Request(url.toString(), requestInit);
    const response = await auth.handler(webRequest);

    // Set status code
    res.status(response.status || 200);

    // Set headers
    if (response.headers) {
      response.headers.forEach((value, key) => {
        res.setHeader(key, value);
      });
    }

    // Send response
    if (response.body) {
      // If it's a redirect, handle it properly
      if (response.status === 302 || response.status === 301) {
        const location = response.headers.get("location");
        if (location) {
          return res.redirect(location);
        }
      }

      // For JSON responses
      const bodyText = await response.text();
      if (bodyText) {
        try {
          const jsonBody = JSON.parse(bodyText);
          return res.json(jsonBody);
        } catch {
          return res.send(bodyText);
        }
      }
    }

    res.end();
  } catch (error) {
    console.error("Better Auth error:", error);
    res.status(500).json({
      success: false,
      message: "Authentication error",
      error: config.app.isDevelopment ? error : undefined,
    });
  }
});

export default router;
