# Google OAuth Setup Guide

This guide explains how to set up Google OAuth authentication for the Bookmarked application.

## Prerequisites

1. A Google Cloud Platform account
2. A project in Google Cloud Console

## Step 1: Create Google Cloud Project (if needed)

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API (or Google Identity API)

## Step 2: Configure OAuth Consent Screen

1. In Google Cloud Console, go to "APIs & Services" > "OAuth consent screen"
2. Choose "External" user type (unless you have a Google Workspace account)
3. Fill in the required information:
   - App name: "Bookmarked"
   - User support email: Your email
   - Developer contact information: Your email
4. Add scopes (optional for basic auth):
   - `../auth/userinfo.email`
   - `../auth/userinfo.profile`
5. Add test users if in testing mode

## Step 3: Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Choose "Web application"
4. Configure:
   - Name: "Bookmarked Web Client"
   - Authorized JavaScript origins:
     - `http://localhost:5173` (for development)
     - Your production frontend URL
   - Authorized redirect URIs:
     - `http://localhost:3001/api/better-auth/callback/google` (for development)
     - Your production backend URL + `/api/better-auth/callback/google`

## Step 4: Configure Environment Variables

1. Copy your Client ID and Client Secret from Google Cloud Console
2. Add them to your `.env` file in the backend directory:

```env
# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id-here
GOOGLE_CLIENT_SECRET=your-google-client-secret-here
```

## Step 5: Test the Integration

1. Start your backend server: `cd backend && yarn dev`
2. Start your frontend server: `cd frontend && yarn dev`
3. Go to the login page and click "Continue with Google"
4. You should be redirected to Google's OAuth flow

## Troubleshooting

### Common Issues:

1. **"redirect_uri_mismatch" error**:
   - Check that your redirect URI in Google Cloud Console matches exactly
   - Make sure to include the full path: `/api/better-auth/callback/google`

2. **"invalid_client" error**:
   - Verify your Client ID and Client Secret are correct
   - Check that the OAuth consent screen is properly configured

3. **CORS errors**:
   - Ensure your frontend URL is added to "Authorized JavaScript origins"

### Development vs Production:

- For development: Use `http://localhost:3001` and `http://localhost:5173`
- For production: Use your actual domain names with HTTPS

## Security Notes

- Never commit your `.env` file with real credentials
- Use different OAuth clients for development and production
- Regularly rotate your client secrets
- Monitor OAuth usage in Google Cloud Console
